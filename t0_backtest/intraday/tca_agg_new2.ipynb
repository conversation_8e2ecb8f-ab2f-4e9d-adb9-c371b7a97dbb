import pandas as pd
from utils import mysql
import numpy as np
pd.set_option('display.max_columns', None)

def fmt_td(delta):
    total_seconds = int(delta.total_seconds())
    # 分解为小时、分钟、秒
    hours, remainder = divmod(total_seconds, 3600)
    minutes, seconds = divmod(remainder, 60)
    formatted_time = f"{hours:02d}:{minutes:02d}:{seconds:02d}"
    return formatted_time

def show_by_name(datas,groups,stats_func):
    l=[]
    for gn in groups:
        if gn=='total':
            r=stats_func(datas)
            r['group']=gn
            l.append(r)
        else: 
            for n,g in datas.groupby(gn):
                r=stats_func(g)
                r['group']=n
                l.append(r)
    return pd.DataFrame(l)

def show_by_multi_name(datas,grp,stats_func):
    l=[]   
    for names,g in datas.groupby(grp):
        r=stats_func(g)
        for i,n in enumerate(names):
            r['group_{}'.format(i)]=n
        l.append(r)
    return pd.DataFrame(l)

def stats(datas):
    d={}
    d['executed_value']=datas['executed_notional'].sum()
    d['no_of_porders']=len(datas)
    d['po_amt_mean']=(datas['arrival_px']*datas['quantity']).mean()
    d['po_amt_med']=(datas['arrival_px']*datas['quantity']).median()
    d['po_lots_mean']=(datas['quantity']/datas['lot_size']).mean()
    d['po_lots_med']=(datas['quantity']/datas['lot_size']).median()
    d['arrival_cost']=-np.average(datas['arrival_cost'],weights=datas['executed_notional']/d['executed_value'])
    d['open_cost']=-np.average(datas['open_cost'],weights=datas['executed_notional']/d['executed_value'])
    d['close_cost']=-np.average(datas['close_cost'],weights=datas['executed_notional']/d['executed_value'])
    d['vwap_cost']=-np.average(datas['vwap_cost'],weights=datas['executed_notional']/d['executed_value'])
    d['vwap_cost2']=-np.average(datas['vwap_cost2'],weights=datas['executed_notional2']/datas['executed_notional2'].sum())
    d['avg_cpt_rate']=datas['cplt_rate'].mean()
    d['wavg_cpt_rate']=np.average(datas['cplt_rate'],weights=(datas['arrival_px']*datas['quantity'])/(datas['arrival_px']*datas['quantity']).sum())
    if datas['cancel_slices'].sum()==0:
        d['cxl_rate']=0
    else:
        d['cxl_rate']=datas['cancel_slices'].sum()/(datas['total_slices']-datas['error_slices']).sum()
    d['target_exe_time']=fmt_td((datas['target_exe_time']).mean())
    d['avg_order_size']=datas['avg_order_size'].mean()
    d['avg_order_value']=(datas['executed_notional']/(datas['total_slices']-datas['error_slices'])).mean()
    # d['avg_filled_time']=datas['avg_filled_time'].mean()
    # d['avg_cancel_time']=datas['avg_cancel_time'].mean()
    d['win_r']=datas['win_rate'].mean()
    return d

def read_datas(sd,ed,algo_provider=None,firm=None):
    sql="select * from algo_parentorder where date>='{}' and date<='{}'".format(sd,ed)
    sql+= " and algo_provider='{}'".format(algo_provider) if algo_provider is not None else ""
    sql+= " and firm='{}'".format(firm) if firm is not None else ""
    pos=mysql.query(mysql.get_zs_trading_data_db_connection(),sql)
    tca1=mysql.query(mysql.get_zs_trading_data_db_connection(),"select * from tca_basic_analysis where id in {}".format(tuple(pos['id'].values)))
    tca2=mysql.query(mysql.get_zs_trading_data_db_connection(),"select * from tca_other_analysis where id in {}".format(tuple(pos['id'].values)))
    tca3=mysql.query(mysql.get_zs_trading_data_db_connection(),"select * from tca_open_analysis where id in {}".format(tuple(pos['id'].values)))
    datas=pos.merge(tca1,on='id',how='left').merge(tca2,on='id',how='left').merge(tca3,on='id',how='left')
    datas=datas.fillna(0)
    datas['date']=datas['date'].astype(str)
    datas['executed_notional']=datas['filled_quantity']*datas['filled_price']
    datas['start_time']=pd.to_datetime(datas['date']+datas['start_time'].apply(lambda x:str(x).zfill(6)))
    datas['end_time']=pd.to_datetime(datas['date']+datas['end_time'].apply(lambda x:str(x).zfill(6)))
    datas['target_exe_time']=datas['end_time']-datas['start_time']
    datas['filled_price2']=np.where((datas['filled_quantity']<datas['quantity'])&(datas['oppo_px']>0),((datas['filled_quantity']*datas['filled_price'])+datas['oppo_px']*(datas['quantity']-datas['filled_quantity']))/(datas['quantity']),datas['filled_price'])
    datas['executed_notional2']=datas['filled_price2']*datas['quantity']
    datas['vwap_cost2']=(datas['side']*(datas['filled_price2']/datas['vwap'] -1)*10000).fillna(0)
    datas['open_cost']=(datas['side']*(datas['filled_price']/datas['open_px'] -1)*10000).fillna(0)
    datas['close_cost']=(datas['side']*(datas['filled_price']/datas['close_px'] -1)*10000).fillna(0)
    datas['lot_size']=datas['symbol'].apply(lambda x:200 if x[:3]=='688' else 100)
    datas=datas[datas['side']!=0]
    return datas

datas=read_datas("********","********")

df=show_by_multi_name(datas[datas['sys_type']!='xtp'],['account_id','date'],stats)

df.head()

df[df['group_1']=="********"]

show_by_name(datas,['total','side','firm','date'],stats).to_csv(r"./tca.csv")

show_by_name(datas[datas['firm']=='wuzhi'],['total','side','firm','date'],stats).to_csv(r"./tca.csv")

show_by_name(datas[datas['firm']=='wuzhi'],['total','side','firm','date'],stats).to_csv("wuzhi.csv",index=False)

show_by_name(datas[datas['firm']=='kuanfu'],['total','side','firm','date'],stats).to_csv("kuanfu.csv",index=False)

********/(********+********.67) * 1.38 +********/(********+********.67)*5.9

show_by_name(datas[datas['firm']=='kuanfu'],['total','side','firm','date'],stats)

show_by_name(datas[(datas['firm']=='wuzhi')&(datas['date']=='********')],['total','side','firm','date'],stats)

show_by_name(datas[(datas['firm']=='kuanfu')&(datas['date']<'********')],['total','side','firm','date'],stats)

show_by_name(datas[(datas['firm']=='kuanfu')&(datas['date']>='********')],['total','side','firm','date'],stats)

122/(122+905) *12 +905/(122+905) *-0.36

525/(1046+525)*-9.5

1046/(1046+525)*5.95



3-31~4-3 0.78
4-7~4-11 0.8
all 0.8



show_by_name(datas,['total','side','firm','date'],stats).to_csv(r"./guojun_tca.csv",index=False,encoding='utf-8-sig')

show_by_name(datas[datas['firm']=='kuanfu'],['total','side','firm','date'],stats)



show_by_name(datas,['total','side','firm','date'],stats).to_csv(r"./results/guojun_tca_20250317-20250321.csv",index=False)
for (firm,d) in datas.groupby('firm'):
    show_by_name(d,['total','side','firm','date'],stats).to_csv(r"./results/guojun_tca_{}_20250317-20250321.csv".format(firm),index=False)

show_by_name(datas[datas['firm']=='kuanfu'],['total','side','firm','date'],stats)

show_by_name(datas[datas['firm']=='kuanfu'],['total','side','firm','date'],stats).to_csv(r"./results/tca_kuanfu_20250306-20250312.csv",index=False)

show_by_name(datas[datas['firm']=='wuzhi'],['total','side','date'],stats).to_csv(r"./results/tca_wuzhi_20250306-20250312.csv",index=False)