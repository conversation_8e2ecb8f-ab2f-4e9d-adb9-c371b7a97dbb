import os
import sys
import pandas as pd
from utils import mysql
import numpy as np
import matplotlib.pyplot as plt

import warnings
pd.set_option('display.max_columns', None)

def fmt_td(delta):
    total_seconds = int(delta.total_seconds())
    # 分解为小时、分钟、秒
    hours, remainder = divmod(total_seconds, 3600)
    minutes, seconds = divmod(remainder, 60)
    formatted_time = f"{hours:02d}:{minutes:02d}:{seconds:02d}"
    return formatted_time

def show_by_name(datas,groups,stats_func):
    l=[]
    for gn in groups:
        if gn=='total':
            r=stats_func(datas)
            r['group']=gn
            l.append(r)
        else: 
            for n,g in datas.groupby(gn):
                r=stats_func(g)
                r['group']=n
                l.append(r)
    return pd.DataFrame(l)

def show_by_multi_name(datas,grp,stats_func):
    l=[]   
    for names,g in datas.groupby(grp):
        r=stats_func(g)
        for i,n in enumerate(names):
            r['group_{}'.format(i)]=n
        l.append(r)
    return pd.DataFrame(l)

def stats(datas):
    d={}
    d['executed_value']=datas['executed_notional'].sum()
    d['no_of_porders']=len(datas)
    d['po_amt_mean']=(datas['arrival_px']*datas['quantity']).mean()
    d['po_amt_med']=(datas['arrival_px']*datas['quantity']).median()
    d['po_lots_mean']=(datas['quantity']/datas['lot_size']).mean()
    d['po_lots_med']=(datas['quantity']/datas['lot_size']).median()
    d['arrival_cost']=-np.average(datas['arrival_cost'],weights=datas['executed_notional']/d['executed_value'])
    d['open_cost']=-np.average(datas['open_cost'],weights=datas['executed_notional']/d['executed_value'])
    d['close_cost']=-np.average(datas['close_cost'],weights=datas['executed_notional']/d['executed_value'])
    d['vwap_cost']=-np.average(datas['vwap_cost'],weights=datas['executed_notional']/d['executed_value'])
    d['vwap_cost2']=-np.average(datas['vwap_cost2'],weights=datas['executed_notional2']/datas['executed_notional2'].sum())
    d['avg_cpt_rate']=datas['cplt_rate'].mean()
    d['wavg_cpt_rate']=np.average(datas['cplt_rate'],weights=(datas['arrival_px']*datas['quantity'])/(datas['arrival_px']*datas['quantity']).sum())
    if datas['cancel_slices'].sum()==0:
        d['cxl_rate']=0
    else:
        d['cxl_rate']=datas['cancel_slices'].sum()/(datas['total_slices']-datas['error_slices']).sum()
    d['target_exe_time']=fmt_td((datas['target_exe_time']).mean())
    d['avg_order_size']=datas['avg_order_size'].mean()
    d['avg_order_value']=(datas['executed_notional']/(datas['total_slices']-datas['error_slices'])).mean()
    # d['avg_filled_time']=datas['avg_filled_time'].mean()
    # d['avg_cancel_time']=datas['avg_cancel_time'].mean()
    d['win_r']=datas['win_rate'].mean()
    d['num_order']=len(datas)
    return d

def read_datas(sd,ed,algo_provider=None,firm=None):
    sql="select * from algo_parentorder where date>='{}' and date<='{}'".format(sd,ed)
    sql+= " and algo_provider='{}'".format(algo_provider) if algo_provider is not None else ""
    sql+= " and firm='{}'".format(firm) if firm is not None else ""
    pos=mysql.query(mysql.get_zs_trading_data_db_connection(),sql)
    tca1=mysql.query(mysql.get_zs_trading_data_db_connection(),"select * from tca_basic_analysis where id in {}".format(tuple(pos['id'].values)))
    tca2=mysql.query(mysql.get_zs_trading_data_db_connection(),"select * from tca_other_analysis where id in {}".format(tuple(pos['id'].values)))
    tca3=mysql.query(mysql.get_zs_trading_data_db_connection(),"select * from tca_open_analysis where id in {}".format(tuple(pos['id'].values)))
    datas=pos.merge(tca1,on='id',how='left').merge(tca2,on='id',how='left').merge(tca3,on='id',how='left')
    datas=datas.fillna(0)
    datas['date']=datas['date'].astype(str)
    datas['executed_notional']=datas['filled_quantity']*datas['filled_price']
    datas['start_time']=pd.to_datetime(datas['date']+datas['start_time'].apply(lambda x:str(x).zfill(6)))
    datas['end_time']=pd.to_datetime(datas['date']+datas['end_time'].apply(lambda x:str(x).zfill(6)))
    datas['target_exe_time']=datas['end_time']-datas['start_time']
    datas['filled_price2']=np.where((datas['filled_quantity']<datas['quantity'])&(datas['oppo_px']>0),((datas['filled_quantity']*datas['filled_price'])+datas['oppo_px']*(datas['quantity']-datas['filled_quantity']))/(datas['quantity']),datas['filled_price'])
    datas['executed_notional2']=datas['filled_price2']*datas['quantity']
    datas['vwap_cost2']=(datas['side']*(datas['filled_price2']/datas['vwap'] -1)*10000).fillna(0)
    datas['open_cost']=(datas['side']*(datas['filled_price']/datas['open_px'] -1)*10000).fillna(0)
    datas['close_cost']=(datas['side']*(datas['filled_price']/datas['close_px'] -1)*10000).fillna(0)
    datas['lot_size']=datas['symbol'].apply(lambda x:200 if x[:3]=='688' else 100)
    datas=datas[datas['side']!=0]
    return datas



from pathlib import Path
# notebook_dir = Path.cwd() 

# print(str(notebook_dir.parent))
# sys.path.insert(0, str(notebook_dir.parent))
# from utils import mysql
# sys.path.remove(str(notebook_dir.parent))


# print(str(notebook_dir.parent.parent))
# sys.path.insert(0, str(notebook_dir.parent.parent))
# from misc.ssh_conn import ftp_clent_zx_zhongtai
# from misc.Readstockfile import read_remote_file, write_file
# sys.path.remove(str(notebook_dir.parent.parent))


cur_dir = Path.cwd()
p_dir = os.path.join(cur_dir, '../../')
sys.path.insert(0, p_dir)
from data_utils.trading_calendar import Calendar
from misc.Readstockfile import update_xlsx_putdf
from misc import tools

sys.path.remove(p_dir)

warnings.filterwarnings('ignore') 




def datas_add_cons_group(datas):
    # sql = "select * from algo_parentorder where date>='{}' and date<='{}'".format(datas['date'].min(), datas['date'].max())
    # algo_parentorder = pd.read_sql(sql, mysql.get_zs_trading_data_db_connection())
    
    # datas = datas.merge(algo_parentorder, on='id', how='left')
    l = []
    dates = datas['date'].unique()
    for date in dates:
        cons_tag = tools.get_cons_tag_series(date)
        cons = cons_tag.to_frame('index_name').reset_index()
        cons['ticker'] = cons['ticker'].astype(str).str.zfill(6)
        cons.rename(columns={'ticker': 'symbol'}, inplace=True)
        cons['date'] = date
        l.append(cons)
    if len(l) > 0:
        cons = pd.concat(l, axis=0, ignore_index=True)
    else:
        cons = pd.DataFrame(columns=['symbol', 'date', 'index_name'])
        
    datas = datas.merge(cons, on=['symbol', 'date'], how='left')
    datas['index_name'].fillna('OTHER', inplace=True)
    
    return datas

def datas_add_some_group(datas):
    import datetime
    # price_groups = {
    #     'extreme_low': (0.0, 2.00),
    #     'low': (2.00, 4.00),
    #     'mid': (4.00, 10.00),
    #     'high': (10.00, 20.00),
    #     'extreme_high': (20.00, 9999.00),
    # }
    # datas['price_group'] = np.nan
    # for group_name, group_range in price_groups.items():
    #     datas.loc[(datas['close_px'] >= group_range[0]) & (datas['close_px'] < group_range[1]), 'price_group'] = group_name
        
    bins =   [0.0, 2.00, 4.00, 10.00, 20.00, 9999.00]
    labels = ['extreme_low', 'low', 'mid', 'high', 'extreme_high']
    datas['price_group'] = pd.cut(datas['close_px'], bins=bins, labels=labels, right=False)
    
    # add interval column
    bins = [datetime.timedelta(minutes=i) for i in [0, 15, 30, 60, 240]]
    labels = ['0<t<=15m', '15m<t<=30m', '30m<t<=1h', '1h<t<=4h']
    datas['interval_group'] = pd.cut(datas['date'], bins=bins, labels=labels, right=True)
    
    bins = [0, 500, 1000, 5000, 9999999]
    labels = ['100-500股', '500-1000股', '1000-5000股', '5000股以上']
    datas['po_volume_group'] = pd.cut(datas['quantity'], bins=bins, labels=labels, right=True)
    
    return datas


def datas_add_turnover_rate_group(datas):
    l = []
    dates = datas['date'].unique()
    for date in dates:
        tag = tools.get_turnover_rate_by_date(date)

        tag['ticker'] = tag['ticker'].astype(str).str.zfill(6)
        tag.rename(columns={'ticker': 'symbol'}, inplace=True)
        tag['date'] = date
        l.append(tag)
    if len(l) > 0:
        tag = pd.concat(l, axis=0, ignore_index=True)
    else:
        tag = pd.DataFrame(columns=['symbol', 'date', 'turnover_rate'])
        
    datas = datas.merge(tag, on=['symbol', 'date'], how='left')
    bins = [0, 0.01, 0.10, 0.20, 1.00]
    labels = ['tr_low', 'tr_mid', 'tr_high', 'tr_extreme_high']
    datas['turnover_group'] = pd.cut(datas['turnover_rate'], bins=bins, labels=labels, right=False)
    return datas


print(datas[datas['quantity']< 100])

datas = read_datas('20250615', '20250623')

datas = datas_add_cons_group(datas)

datas = datas_add_some_group(datas)

datas = datas_add_turnover_rate_group(datas)

print(datas.head(10))

total = show_by_name(datas, ['total', 'turnover_group'], stats)
print(total)

total_orders = len(datas)
total_value = datas['executed_notional'].sum()
avg_vwap_bps = np.average(datas['vwap_cost'], weights=datas['executed_notional'] / total_value)
total_vwap_revenue = (datas['vwap_cost'] * datas['executed_notional'] / 10000).sum()

print(f'Total Parent Orders: {total_orders}')
print(f'Total Transaction Value (yuan): {total_value:,.2f}')
print(f'Average VWAP Performance (bps): {avg_vwap_bps:.2f}')
print(f'Total VWAP Revenue (yuan): {total_vwap_revenue:,.2f}')

daily_summary = datas.groupby('date').agg(
    total_value=('executed_notional', 'sum'),
    avg_vwap=('vwap_cost', lambda x: np.average(x, weights=datas.loc[x.index, 'executed_notional'])),
    order_count=('id', 'count')
).reset_index()

fig, ax1 = plt.subplots(figsize=(12, 6))

ax1.bar(daily_summary['date'], daily_summary['total_value'] / 10000, color='red', label='Transaction Volume (10k yuan)')
ax1.set_xlabel('Date')
ax1.set_ylabel('Transaction Volume (10k yuan)', color='red')
ax1.tick_params(axis='y', labelcolor='red')

ax2 = ax1.twinx()
ax2.plot(daily_summary['date'], daily_summary['avg_vwap'], color='gold', marker='o', label='VWAP Performance (bps)')
ax2.set_ylabel('VWAP Performance (bps)', color='gold')
ax2.tick_params(axis='y', labelcolor='gold')

plt.title('Daily Transaction Performance')
fig.tight_layout()
plt.show()

by_day = show_by_name(datas=datas, groups=['date'], stats_func=stats)
print(by_day.head())

print(daily_summary.head(5))

# 中文字段映射字典
field_name_mapping = {
    # 分组字段
    'index_name': '指数成分',
    'price_group': '价格分组',
    'interval_group': '时间间隔',
    'po_volume_group': '订单规模',
    'turnover_group': '换手率分组',
    'date': '日期',
    
    # 统计指标
    'executed_value': '成交金额',
    'no_of_porders': '订单数量',
    'po_amt_mean': '平均订单金额',
    'po_amt_med': '订单金额中位数',
    'po_lots_mean': '平均订单手数',
    'po_lots_med': '订单手数中位数',
    'arrival_cost': '到达成本',
    'open_cost': '开盘成本',
    'close_cost': '收盘成本',
    'vwap_cost': 'VWAP成本',
    'vwap_cost2': 'VWAP成本2',
    'avg_cpt_rate': '平均完成率',
    'wavg_cpt_rate': '加权平均完成率',
    'cxl_rate': '撤单率',
    'target_exe_time': '目标执行时间',
    'avg_order_size': '平均订单规模',
    'avg_order_value': '平均订单价值',
    'win_r': '胜率',
    'num_order': '订单总数',
    
    # 分组值映射
    'ZZ1000': '中证1000',
    'ZZ2000': '中证2000', 
    'HS300': '沪深300',
    'OTHER': '其他',
    'extreme_low': '极低价',
    'low': '低价',
    'mid': '中价',
    'high': '高价',
    'extreme_high': '极高价',
    '0<t<=15m': '15分钟内',
    '15m<t<=30m': '15-30分钟',
    '30m<t<=1h': '30分钟-1小时',
    '1h<t<=4h': '1-4小时',
    '100-500股': '100-500股',
    '500-1000股': '500-1000股',
    '1000-5000股': '1000-5000股',
    '5000股以上': '5000股以上',
    'tr_low': '低换手率',
    'tr_mid': '中换手率',
    'tr_high': '高换手率',
    'tr_extreme_high': '极高换手率'
}

def translate_field_name(field_name):
    """将英文字段名转换为中文"""
    return field_name_mapping.get(field_name, field_name)

def translate_group_values(df, column):
    """将分组值转换为中文"""
    if column in df.columns:
        df[column] = df[column].map(lambda x: field_name_mapping.get(str(x), str(x)))
    return df

# 时间序列数据处理函数
def prepare_time_series_data(datas, group_field):
    """准备时间序列分析数据"""
    # 按日期和分组字段进行聚合
    time_series_data = datas.groupby(['date', group_field]).agg({
        'executed_notional': 'sum',
        'id': 'count',
        'vwap_cost': lambda x: np.average(x, weights=datas.loc[x.index, 'executed_notional']),
        'arrival_cost': lambda x: np.average(x, weights=datas.loc[x.index, 'executed_notional']),
        'cplt_rate': 'mean',
        'cxl_rate': 'mean'
    }).reset_index()
    
    # 重命名列
    time_series_data.columns = ['date', group_field, 'total_value', 'order_count', 
                               'avg_vwap_cost', 'avg_arrival_cost', 'avg_completion_rate', 'avg_cancel_rate']
    
    return time_series_data

def generate_group_summary_table(datas, group_field):
    """生成分组汇总统计表"""
    summary = show_by_name(datas, [group_field], stats)
    
    # 转换分组值为中文
    summary = translate_group_values(summary, 'group')
    
    # 选择关键指标
    key_columns = ['group', 'executed_value', 'no_of_porders', 'vwap_cost', 
                   'arrival_cost', 'avg_cpt_rate', 'cxl_rate', 'win_r']
    summary_display = summary[key_columns].copy()
    
    # 格式化数值
    summary_display['executed_value'] = summary_display['executed_value'].apply(lambda x: f'{x/10000:.1f}万')
    summary_display['vwap_cost'] = summary_display['vwap_cost'].apply(lambda x: f'{x:.2f}bp')
    summary_display['arrival_cost'] = summary_display['arrival_cost'].apply(lambda x: f'{x:.2f}bp')
    summary_display['avg_cpt_rate'] = summary_display['avg_cpt_rate'].apply(lambda x: f'{x*100:.1f}%')
    summary_display['cxl_rate'] = summary_display['cxl_rate'].apply(lambda x: f'{x*100:.1f}%')
    summary_display['win_r'] = summary_display['win_r'].apply(lambda x: f'{x*100:.1f}%')
    
    # 重命名列为中文
    summary_display.columns = ['分组', '成交金额', '订单数量', 'VWAP成本', '到达成本', '完成率', '撤单率', '胜率']
    
    return summary_display

# 图表绘制函数
import matplotlib.pyplot as plt
import seaborn as sns
from matplotlib import rcParams

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

def plot_bar_chart(summary_data, group_field, title_suffix=''):
    """绘制柱状图"""
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle(f'{translate_field_name(group_field)}分组统计{title_suffix}', fontsize=16, fontweight='bold')
    
    # 成交金额柱状图
    ax1.bar(summary_data['分组'], summary_data['成交金额'].str.replace('万', '').astype(float), color='steelblue')
    ax1.set_title('成交金额分布', fontweight='bold')
    ax1.set_ylabel('成交金额(万元)')
    ax1.tick_params(axis='x', rotation=45)
    
    # 订单数量柱状图
    ax2.bar(summary_data['分组'], summary_data['订单数量'], color='lightcoral')
    ax2.set_title('订单数量分布', fontweight='bold')
    ax2.set_ylabel('订单数量')
    ax2.tick_params(axis='x', rotation=45)
    
    # VWAP成本柱状图
    vwap_values = summary_data['VWAP成本'].str.replace('bp', '').astype(float)
    colors = ['green' if x < 0 else 'red' for x in vwap_values]
    ax3.bar(summary_data['分组'], vwap_values, color=colors)
    ax3.set_title('VWAP成本分布', fontweight='bold')
    ax3.set_ylabel('VWAP成本(bp)')
    ax3.tick_params(axis='x', rotation=45)
    ax3.axhline(y=0, color='black', linestyle='-', alpha=0.3)
    
    # 完成率柱状图
    ax4.bar(summary_data['分组'], summary_data['完成率'].str.replace('%', '').astype(float), color='gold')
    ax4.set_title('完成率分布', fontweight='bold')
    ax4.set_ylabel('完成率(%)')
    ax4.tick_params(axis='x', rotation=45)
    
    plt.tight_layout()
    plt.show()

def plot_pie_chart(summary_data, group_field, metric='成交金额'):
    """绘制饼图"""
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    fig.suptitle(f'{translate_field_name(group_field)}分组占比分析', fontsize=16, fontweight='bold')
    
    # 成交金额占比饼图
    values1 = summary_data['成交金额'].str.replace('万', '').astype(float)
    ax1.pie(values1, labels=summary_data['分组'], autopct='%1.1f%%', startangle=90)
    ax1.set_title('成交金额占比', fontweight='bold')
    
    # 订单数量占比饼图
    values2 = summary_data['订单数量']
    ax2.pie(values2, labels=summary_data['分组'], autopct='%1.1f%%', startangle=90)
    ax2.set_title('订单数量占比', fontweight='bold')
    
    plt.tight_layout()
    plt.show()

def plot_time_series(datas, group_field):
    """绘制时间序列图"""
    time_data = prepare_time_series_data(datas, group_field)
    
    # 转换分组值为中文
    time_data = translate_group_values(time_data, group_field)
    
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle(f'{translate_field_name(group_field)}时间序列分析', fontsize=16, fontweight='bold')
    
    # 成交金额时间序列
    for group in time_data[group_field].unique():
        group_data = time_data[time_data[group_field] == group]
        ax1.plot(group_data['date'], group_data['total_value']/10000, marker='o', label=group, linewidth=2)
    ax1.set_title('成交金额时间序列', fontweight='bold')
    ax1.set_ylabel('成交金额(万元)')
    ax1.legend()
    ax1.tick_params(axis='x', rotation=45)
    ax1.grid(True, alpha=0.3)
    
    # 订单数量时间序列
    for group in time_data[group_field].unique():
        group_data = time_data[time_data[group_field] == group]
        ax2.plot(group_data['date'], group_data['order_count'], marker='s', label=group, linewidth=2)
    ax2.set_title('订单数量时间序列', fontweight='bold')
    ax2.set_ylabel('订单数量')
    ax2.legend()
    ax2.tick_params(axis='x', rotation=45)
    ax2.grid(True, alpha=0.3)
    
    # VWAP成本时间序列
    for group in time_data[group_field].unique():
        group_data = time_data[time_data[group_field] == group]
        ax3.plot(group_data['date'], group_data['avg_vwap_cost'], marker='^', label=group, linewidth=2)
    ax3.set_title('VWAP成本时间序列', fontweight='bold')
    ax3.set_ylabel('VWAP成本(bp)')
    ax3.legend()
    ax3.tick_params(axis='x', rotation=45)
    ax3.axhline(y=0, color='black', linestyle='--', alpha=0.5)
    ax3.grid(True, alpha=0.3)
    
    # 完成率时间序列
    for group in time_data[group_field].unique():
        group_data = time_data[time_data[group_field] == group]
        ax4.plot(group_data['date'], group_data['avg_completion_rate']*100, marker='d', label=group, linewidth=2)
    ax4.set_title('完成率时间序列', fontweight='bold')
    ax4.set_ylabel('完成率(%)')
    ax4.legend()
    ax4.tick_params(axis='x', rotation=45)
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()

def generate_comprehensive_analysis(datas, group_field):
    """生成综合分析报告"""
    print(f"\n{'='*60}")
    print(f"  {translate_field_name(group_field)}分组综合分析报告")
    print(f"{'='*60}\n")
    
    # 1. 生成汇总统计表
    print("📊 分组统计汇总表")
    print("-" * 40)
    summary_table = generate_group_summary_table(datas, group_field)
    print(summary_table.to_string(index=False))
    print("\n")
    
    # 2. 绘制柱状图
    print("📈 分组统计柱状图")
    print("-" * 40)
    plot_bar_chart(summary_table, group_field)
    
    # 3. 绘制饼图
    print("🥧 分组占比饼图")
    print("-" * 40)
    plot_pie_chart(summary_table, group_field)
    
    # 4. 绘制时间序列图
    print("📉 时间序列趋势图")
    print("-" * 40)
    plot_time_series(datas, group_field)
    
    print(f"\n✅ {translate_field_name(group_field)}分组分析完成！\n")

# 确保数据已加载并处理完成
if 'datas' not in locals():
    # 如果数据未加载，先加载数据
    datas = read_datas('20250615', '20250623')
    datas = datas_add_cons_group(datas)
    datas = datas_add_some_group(datas)
    datas = datas_add_turnover_rate_group(datas)

print(f"数据加载完成，共 {len(datas)} 条记录")
print(f"时间范围：{datas['date'].min()} 至 {datas['date'].max()}")
print(f"可用分组字段：{['index_name', 'price_group', 'interval_group', 'po_volume_group', 'turnover_group']}")

# 1. 指数成分分组分析
generate_comprehensive_analysis(datas, 'index_name')

# 2. 价格分组分析
generate_comprehensive_analysis(datas, 'price_group')

# 3. 时间间隔分组分析
generate_comprehensive_analysis(datas, 'interval_group')

# 4. 订单规模分组分析
generate_comprehensive_analysis(datas, 'po_volume_group')

# 5. 换手率分组分析
generate_comprehensive_analysis(datas, 'turnover_group')

# 生成总体统计摘要
def generate_overall_summary(datas):
    """生成总体统计摘要"""
    print("\n" + "="*80)
    print("                        📈 TCA分析总体摘要报告 📈")
    print("="*80)
    
    total_orders = len(datas)
    total_value = datas['executed_notional'].sum()
    avg_vwap_bps = np.average(datas['vwap_cost'], weights=datas['executed_notional'] / total_value)
    avg_arrival_bps = np.average(datas['arrival_cost'], weights=datas['executed_notional'] / total_value)
    avg_completion_rate = datas['cplt_rate'].mean()
    avg_cancel_rate = datas['cancel_slices'].sum() / (datas['total_slices'] - datas['error_slices']).sum()
    win_rate = datas['win_rate'].mean()
    
    print(f"\n📊 基础统计：")
    print(f"   • 总订单数：{total_orders:,} 笔")
    print(f"   • 总成交金额：{total_value/100000000:.2f} 亿元")
    print(f"   • 分析时间段：{datas['date'].min()} ~ {datas['date'].max()}")
    print(f"   • 交易日数：{datas['date'].nunique()} 天")
    
    print(f"\n💰 成本分析：")
    print(f"   • 平均VWAP成本：{avg_vwap_bps:.2f} bp")
    print(f"   • 平均到达成本：{avg_arrival_bps:.2f} bp")
    print(f"   • VWAP总收益：{(datas['vwap_cost'] * datas['executed_notional'] / 10000).sum():,.0f} 元")
    
    print(f"\n⚡ 执行效率：")
    print(f"   • 平均完成率：{avg_completion_rate*100:.1f}%")
    print(f"   • 平均撤单率：{avg_cancel_rate*100:.1f}%")
    print(f"   • 胜率：{win_rate*100:.1f}%")
    
    print(f"\n🏆 分组表现排名：")
    
    # 各分组的最佳表现
    group_fields = ['index_name', 'price_group', 'turnover_group']
    for field in group_fields:
        group_stats = show_by_name(datas, [field], stats)
        best_vwap = group_stats.loc[group_stats['vwap_cost'].idxmin()]
        print(f"   • {translate_field_name(field)}最佳VWAP：{translate_field_name(best_vwap['group'])} ({best_vwap['vwap_cost']:.2f}bp)")
    
    print("\n" + "="*80)
    print("                           ✅ 分析报告完成 ✅")
    print("="*80 + "\n")

# 执行总体摘要
generate_overall_summary(datas)