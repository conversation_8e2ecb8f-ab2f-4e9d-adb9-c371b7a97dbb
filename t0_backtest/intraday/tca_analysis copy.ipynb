import os
import sys
import pandas as pd
from utils import mysql
import numpy as np
import matplotlib.pyplot as plt

import warnings
pd.set_option('display.max_columns', None)

def fmt_td(delta):
    total_seconds = int(delta.total_seconds())
    # 分解为小时、分钟、秒
    hours, remainder = divmod(total_seconds, 3600)
    minutes, seconds = divmod(remainder, 60)
    formatted_time = f"{hours:02d}:{minutes:02d}:{seconds:02d}"
    return formatted_time

def show_by_name(datas,groups,stats_func):
    l=[]
    for gn in groups:
        if gn=='total':
            r=stats_func(datas)
            r['group']=gn
            l.append(r)
        else: 
            for n,g in datas.groupby(gn):
                r=stats_func(g)
                r['group']=n
                l.append(r)
    return pd.DataFrame(l)

def show_by_multi_name(datas,grp,stats_func):
    l=[]   
    for names,g in datas.groupby(grp):
        r=stats_func(g)
        for i,n in enumerate(names):
            r['group_{}'.format(i)]=n
        l.append(r)
    return pd.DataFrame(l)

def stats(datas):
    d={}
    d['executed_value']=datas['executed_notional'].sum()
    d['no_of_porders']=len(datas)
    d['po_amt_mean']=(datas['arrival_px']*datas['quantity']).mean()
    d['po_amt_med']=(datas['arrival_px']*datas['quantity']).median()
    d['po_lots_mean']=(datas['quantity']/datas['lot_size']).mean()
    d['po_lots_med']=(datas['quantity']/datas['lot_size']).median()
    d['arrival_cost']=-np.average(datas['arrival_cost'],weights=datas['executed_notional']/d['executed_value'])
    d['open_cost']=-np.average(datas['open_cost'],weights=datas['executed_notional']/d['executed_value'])
    d['close_cost']=-np.average(datas['close_cost'],weights=datas['executed_notional']/d['executed_value'])
    d['vwap_cost']=-np.average(datas['vwap_cost'],weights=datas['executed_notional']/d['executed_value'])
    d['vwap_cost2']=-np.average(datas['vwap_cost2'],weights=datas['executed_notional2']/datas['executed_notional2'].sum())
    d['avg_cpt_rate']=datas['cplt_rate'].mean()
    d['wavg_cpt_rate']=np.average(datas['cplt_rate'],weights=(datas['arrival_px']*datas['quantity'])/(datas['arrival_px']*datas['quantity']).sum())
    if datas['cancel_slices'].sum()==0:
        d['cxl_rate']=0
    else:
        d['cxl_rate']=datas['cancel_slices'].sum()/(datas['total_slices']-datas['error_slices']).sum()
    d['target_exe_time']=fmt_td((datas['target_exe_time']).mean())
    d['avg_order_size']=datas['avg_order_size'].mean()
    d['avg_order_value']=(datas['executed_notional']/(datas['total_slices']-datas['error_slices'])).mean()
    # d['avg_filled_time']=datas['avg_filled_time'].mean()
    # d['avg_cancel_time']=datas['avg_cancel_time'].mean()
    d['win_r']=datas['win_rate'].mean()
    d['num_order']=len(datas)
    return d

def read_datas(sd,ed,algo_provider=None,firm=None):
    sql="select * from algo_parentorder where date>='{}' and date<='{}'".format(sd,ed)
    sql+= " and algo_provider='{}'".format(algo_provider) if algo_provider is not None else ""
    sql+= " and firm='{}'".format(firm) if firm is not None else ""
    pos=mysql.query(mysql.get_zs_trading_data_db_connection(),sql)
    tca1=mysql.query(mysql.get_zs_trading_data_db_connection(),"select * from tca_basic_analysis where id in {}".format(tuple(pos['id'].values)))
    tca2=mysql.query(mysql.get_zs_trading_data_db_connection(),"select * from tca_other_analysis where id in {}".format(tuple(pos['id'].values)))
    tca3=mysql.query(mysql.get_zs_trading_data_db_connection(),"select * from tca_open_analysis where id in {}".format(tuple(pos['id'].values)))
    datas=pos.merge(tca1,on='id',how='left').merge(tca2,on='id',how='left').merge(tca3,on='id',how='left')
    datas=datas.fillna(0)
    datas['date']=datas['date'].astype(str)
    datas['executed_notional']=datas['filled_quantity']*datas['filled_price']
    datas['start_time']=pd.to_datetime(datas['date']+datas['start_time'].apply(lambda x:str(x).zfill(6)))
    datas['end_time']=pd.to_datetime(datas['date']+datas['end_time'].apply(lambda x:str(x).zfill(6)))
    datas['target_exe_time']=datas['end_time']-datas['start_time']
    datas['filled_price2']=np.where((datas['filled_quantity']<datas['quantity'])&(datas['oppo_px']>0),((datas['filled_quantity']*datas['filled_price'])+datas['oppo_px']*(datas['quantity']-datas['filled_quantity']))/(datas['quantity']),datas['filled_price'])
    datas['executed_notional2']=datas['filled_price2']*datas['quantity']
    datas['vwap_cost2']=(datas['side']*(datas['filled_price2']/datas['vwap'] -1)*10000).fillna(0)
    datas['open_cost']=(datas['side']*(datas['filled_price']/datas['open_px'] -1)*10000).fillna(0)
    datas['close_cost']=(datas['side']*(datas['filled_price']/datas['close_px'] -1)*10000).fillna(0)
    datas['lot_size']=datas['symbol'].apply(lambda x:200 if x[:3]=='688' else 100)
    datas=datas[datas['side']!=0]
    return datas



from pathlib import Path
# notebook_dir = Path.cwd() 

# print(str(notebook_dir.parent))
# sys.path.insert(0, str(notebook_dir.parent))
# from utils import mysql
# sys.path.remove(str(notebook_dir.parent))


# print(str(notebook_dir.parent.parent))
# sys.path.insert(0, str(notebook_dir.parent.parent))
# from misc.ssh_conn import ftp_clent_zx_zhongtai
# from misc.Readstockfile import read_remote_file, write_file
# sys.path.remove(str(notebook_dir.parent.parent))


cur_dir = Path.cwd()
p_dir = os.path.join(cur_dir, '../../')
sys.path.insert(0, p_dir)
from data_utils.trading_calendar import Calendar
from misc.Readstockfile import update_xlsx_putdf
from misc import tools

sys.path.remove(p_dir)

warnings.filterwarnings('ignore') 




def datas_add_cons_group(datas):
    # sql = "select * from algo_parentorder where date>='{}' and date<='{}'".format(datas['date'].min(), datas['date'].max())
    # algo_parentorder = pd.read_sql(sql, mysql.get_zs_trading_data_db_connection())
    
    # datas = datas.merge(algo_parentorder, on='id', how='left')
    l = []
    dates = datas['date'].unique()
    for date in dates:
        cons_tag = tools.get_cons_tag_series(date)
        cons = cons_tag.to_frame('index_name').reset_index()
        cons['ticker'] = cons['ticker'].astype(str).str.zfill(6)
        cons.rename(columns={'ticker': 'symbol'}, inplace=True)
        cons['date'] = date
        l.append(cons)
    if len(l) > 0:
        cons = pd.concat(l, axis=0, ignore_index=True)
    else:
        cons = pd.DataFrame(columns=['symbol', 'date', 'index_name'])
        
    datas = datas.merge(cons, on=['symbol', 'date'], how='left')
    datas['index_name'].fillna('OTHER', inplace=True)
    
    return datas

def datas_add_some_group(datas):
    import datetime
    # price_groups = {
    #     'extreme_low': (0.0, 2.00),
    #     'low': (2.00, 4.00),
    #     'mid': (4.00, 10.00),
    #     'high': (10.00, 20.00),
    #     'extreme_high': (20.00, 9999.00),
    # }
    # datas['price_group'] = np.nan
    # for group_name, group_range in price_groups.items():
    #     datas.loc[(datas['close_px'] >= group_range[0]) & (datas['close_px'] < group_range[1]), 'price_group'] = group_name
        
    bins =   [0.0, 2.00, 4.00, 10.00, 20.00, 9999.00]
    labels = ['extreme_low', 'low', 'mid', 'high', 'extreme_high']
    datas['price_group'] = pd.cut(datas['close_px'], bins=bins, labels=labels, right=False)
    
    # add interval column
    bins = [datetime.timedelta(minutes=i) for i in [0, 15, 30, 60, 240]]
    labels = ['0<t<=15m', '15m<t<=30m', '30m<t<=1h', '1h<t<=4h']
    datas['interval_group'] = pd.cut(datas['date'], bins=bins, labels=labels, right=True)
    
    bins = [100, 500, 1000, 5000, 9999999]
    labels = ['100-500股', '500-1000股', '1000-5000股', '5000股以上']
    datas['po_volume_group'] = pd.cut(datas['quantity'], bins=bins, labels=labels, right=True)
    
    return datas


def datas_add_turnover_rate_group(datas):
    l = []
    dates = datas['date'].unique()
    for date in dates:
        tag = tools.get_turnover_rate_by_date(date)

        tag['ticker'] = tag['ticker'].astype(str).str.zfill(6)
        tag.rename(columns={'ticker': 'symbol'}, inplace=True)
        tag['date'] = date
        l.append(tag)
    if len(l) > 0:
        tag = pd.concat(l, axis=0, ignore_index=True)
    else:
        tag = pd.DataFrame(columns=['symbol', 'date', 'turnover_rate'])
        
    datas = datas.merge(tag, on=['symbol', 'date'], how='left')
    bins = [0, 0.01, 0.10, 0.20, 1.00]
    labels = ['tr_low', 'tr_mid', 'tr_high', 'tr_extreme_high']
    datas['turnover_group'] = pd.cut(datas['turnover_rate'], bins=bins, labels=labels, right=False)
    return datas




datas = read_datas('20250615', '20250623')

datas = datas_add_cons_group(datas)

datas = datas_add_some_group(datas)

datas = datas_add_turnover_rate_group(datas)

print(datas.head(10))

total = show_by_name(datas, ['total', 'turnover_group'], stats)
print(total)

total_orders = len(datas)
total_value = datas['executed_notional'].sum()
avg_vwap_bps = np.average(datas['vwap_cost'], weights=datas['executed_notional'] / total_value)
total_vwap_revenue = (datas['vwap_cost'] * datas['executed_notional'] / 10000).sum()

print(f'Total Parent Orders: {total_orders}')
print(f'Total Transaction Value (yuan): {total_value:,.2f}')
print(f'Average VWAP Performance (bps): {avg_vwap_bps:.2f}')
print(f'Total VWAP Revenue (yuan): {total_vwap_revenue:,.2f}')

daily_summary = datas.groupby('date').agg(
    total_value=('executed_notional', 'sum'),
    avg_vwap=('vwap_cost', lambda x: np.average(x, weights=datas.loc[x.index, 'executed_notional'])),
    order_count=('id', 'count')
).reset_index()

fig, ax1 = plt.subplots(figsize=(12, 6))

ax1.bar(daily_summary['date'], daily_summary['total_value'] / 10000, color='red', label='Transaction Volume (10k yuan)')
ax1.set_xlabel('Date')
ax1.set_ylabel('Transaction Volume (10k yuan)', color='red')
ax1.tick_params(axis='y', labelcolor='red')

ax2 = ax1.twinx()
ax2.plot(daily_summary['date'], daily_summary['avg_vwap'], color='gold', marker='o', label='VWAP Performance (bps)')
ax2.set_ylabel('VWAP Performance (bps)', color='gold')
ax2.tick_params(axis='y', labelcolor='gold')

plt.title('Daily Transaction Performance')
fig.tight_layout()
plt.show()

by_day = show_by_name(datas=datas, groups=['date'], stats_func=stats)
print(by_day.head())

print(daily_summary.head(5))