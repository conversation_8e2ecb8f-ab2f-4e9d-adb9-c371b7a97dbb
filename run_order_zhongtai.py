import datetime
from duckdb import order
from matplotlib.pyplot import flag
import pandas as pd
import numpy as np
import shutil
import os, sys
import pprint

from misc.ssh_conn import sftp_clent_wintrader, ftp_clent_chaolz, sftp_clent_zt_tuoguan, sftp_clent_zsdav
from misc.tools import get_stock_adj_close
from data_utils.trading_calendar import Calendar
from misc.Readstockfile import read_remote_file
from misc.standardize_dev.zhongtai_smartx import std_hold

from misc.trade import cal_changes

from zt_haomai_uploader import ZTHaoMaiUploader

def get_target_parentorders(work_date=None, suffix=''):
    if work_date is None:
        work_date = datetime.datetime.now().strftime("%Y%m%d")
        # targetPosition_  109156033251_Position{}_Target
    # targetFile = "/home/<USER>/targetPosition_{}.csv".format(work_date,work_date)  
    # targetFile = "targetPosition_{}.csv".format(work_date,work_date)
    targetFile = "/data/shared-data/public/vsftp_data/zs_zhongtai/data/chaolz_zz1000/daily_before/{}/targetPosition_{}{}.csv".format(work_date,work_date,suffix)
    try:
        df = pd.read_csv(targetFile)
        # print(df.head(3))
        return df
    except FileNotFoundError:
        print(f"文件 {targetFile} 未找到。")
    except Exception as e:
        print(f"读取文件时发生错误: {e}")

def GetExchange(sym):
    if sym[:1] == "6":
        return sym+".SH"
    return sym+".SZ"

def stk_lot_size(sym):
    if sym[:3]=="688":
        return 200
    return 100     


def get_target(work_date, suffix=''):
    """
    return symbol, target_shares, direction
    """
    targets = get_target_parentorders(work_date, suffix)
    targets_df = targets.copy()
    targets_df['symbol']=targets_df['symbol'].astype(int)
    targets_df.rename(columns={'target': 'target_shares', }, inplace=True)
    targets_df['direction'] = targets_df['target_shares'].apply(lambda x: 1 if x > 0 else -1)
    targets_df['target_shares'] = targets_df['target_shares'].abs()
    
    return targets_df[['symbol', 'target_shares', 'direction']]


def get_hold(work_date):
    """
    return symbol, hold_shares, available_shares, direction
    """
    hold_file = f'数据导出/中泰SmartX/自动导出/{work_date}/xtp_109156033251_Position.csv'
    df = read_remote_file(hold_file, src_type='wintrader')
    df = std_hold(df)
    df = df[['ticker', 'volume', 'available_volume']].rename(columns={'ticker': 'symbol', 'volume': 'hold_shares', 'available_volume': 'available_shares'})
    df['direction'] = df['hold_shares'].apply(lambda x: 1 if x > 0 else -1)
    df['hold_shares'] = df['hold_shares'].abs()
    return df


def get_hold_from_targetorder(work_date, suffix=''):
    """
    return symbol, hold_shares, available_shares, direction
    """
    targets = get_target_parentorders(work_date, suffix)
    hold = targets.copy()
    
    hold['symbol'] = hold['symbol'].astype(int)
    hold = hold[['symbol', 'position']]
    hold.rename(columns={'position': 'hold_shares'}, inplace=True)
    hold['available_shares'] = hold['hold_shares']
    hold['direction'] = hold['hold_shares'].apply(lambda x: 1 if x > 0 else -1)
    hold['hold_shares'] = hold['hold_shares'].abs()
    return hold


def get_target_as_hold(work_date, suffix=''):
    """
    return symbol, hold_shares, available_shares, direction
    """
    targets = get_target_parentorders(work_date, suffix)
    hold = targets.copy()
    
    hold['symbol'] = hold['symbol'].astype(int)
    hold['available_shares'] = hold[['target', 'position']].min(axis=1)
    hold.rename(columns={'target': 'hold_shares'}, inplace=True)
    hold = hold[['symbol', 'hold_shares', 'available_shares']]
    
    hold['direction'] = hold['hold_shares'].apply(lambda x: 1 if x > 0 else -1)
    hold['hold_shares'] = hold['hold_shares'].abs()
    return hold



def vwap_target(work_date, hold_type='latest', target_suffix='', order_suffix=''):
    targets_df = get_target(work_date, target_suffix)
    
    if hold_type == 'latest':
        hold_df = get_hold(work_date)
    elif hold_type == 'from_target':
        hold_df = get_hold_from_targetorder(work_date, target_suffix)
    elif hold_type == 'as_target':
        hold_df = get_target_as_hold(work_date, target_suffix)
    else:
        raise ValueError(f'hold_type {hold_type} is not supported.')
    
    
    pre_date = Calendar.last_trading_day(work_date).strftime('%Y%m%d')
    pre_close = get_stock_adj_close(pre_date).rename(columns={'ticker': 'symbol',})
    changes, info = cal_changes(hold_df, targets_df, pre_close)
    
    # pprint.pprint(f'info: {info}')
    orders = pd.DataFrame(columns=['type', 'symbol', 'side', 'qty'])
    orders['symbol'] = changes['symbol'].apply(lambda x: str(x).zfill(6))
    orders['symbol'] = orders['symbol'].apply(GetExchange)
    orders['type']= 1
    orders['side']= changes['order_shares'].apply(lambda x: 'b' if x>0 else 's')
    orders['qty'] = changes['order_shares'].abs()
    orders = pd.concat([orders, pd.DataFrame({'type': [0], 'symbol': [pd.NA], 'side': [pd.NA], 'qty': [pd.NA]})], axis=0)
    local_account_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'accounts', '超量子中泰')
    csv_file_path = os.path.join(local_account_dir, f'zhongtai_vwap_target_{work_date}{order_suffix}.csv')
    orders.to_csv(csv_file_path, header=False, index=False, encoding='gbk')
    


def t0_target(work_date, hold_type='latest', target_suffix='', order_suffix=''):
    targets_df = get_target(work_date, target_suffix)
    
    if hold_type == 'latest':
        hold_df = get_hold(work_date)
    elif hold_type == 'from_target':
        hold_df = get_hold_from_targetorder(work_date, target_suffix)
    elif hold_type == 'as_target':
        hold_df = get_target_as_hold(work_date, target_suffix)
    else:
        raise ValueError(f'hold_type {hold_type} is not supported.')
    
    df = pd.concat([targets_df[['symbol', 'target_shares']].set_index('symbol'), hold_df[['symbol', 'available_shares']].set_index('symbol')], axis=1, join='outer')
    df.fillna(0, inplace=True)
    df['t0_shares'] = df[['target_shares', 'available_shares']].min(axis=1)

    # 1
    # df['t0_shares'] = df['t0_shares'] / 2

    # 2
    # df['t0_tmp'] = df['t0_shares'] / 2
    # df['t0_shares'] = df['t0_shares'] - df['t0_tmp']
    
    df['t0_shares'] = df['t0_shares'].div(100).astype('int').mul(100)
    df['t0_shares'] = np.where((df.index >= 688000) & (df['t0_shares'] < 200), 0, df['t0_shares'])
    df = df[['t0_shares']].reset_index()
    df = df[df['t0_shares'] != 0]
    
    orders = pd.DataFrame(columns=['type', 'symbol', 'side', 'qty'])
    orders['symbol'] = df['symbol'].apply(lambda x: str(x).zfill(6)).apply(GetExchange)
    orders['type']= 1
    orders['side']= 's'
    orders['qty'] = df['t0_shares']
    orders = pd.concat([orders, pd.DataFrame({'type': [0], 'symbol': [pd.NA], 'side': [pd.NA], 'qty': [pd.NA]})], axis=0)
    print(f't0 orders:\n{orders.head(5)}')
    print(f't0 total volume: {orders["qty"].sum()}')
    
    local_account_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'accounts', '超量子中泰')
    csv_file_path = os.path.join(local_account_dir, f'zhongtai_t0_target_{work_date}{order_suffix}.csv')
    orders.to_csv(csv_file_path, header=False, index=False, encoding='gbk')


    
    
def ftpcopy_vwap(work_date, suffix=''):
    local_account_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'accounts', '超量子中泰')
    dav_order_dir = os.path.join(f'/home/<USER>/dav', 'accounts', '超量子中泰', 'order')

    # vwap
    shutil.copy(os.path.join(local_account_dir, f'zhongtai_vwap_target_{work_date}{suffix}.csv'),
                os.path.join(dav_order_dir, f'zhongtai_vwap_target_{work_date}{suffix}.csv')
                )
    
    sftp_clent_wintrader.put(local_path=os.path.join(local_account_dir, f'zhongtai_vwap_target_{work_date}{suffix}.csv'),
                             remote_path='交易文件/超量子中泰/zhongtai_vwap_target_{}{}.csv'.format(work_date,suffix)
                             )    
    ftp_clent_chaolz.put(os.path.join(local_account_dir, f'zhongtai_vwap_target_{work_date}{suffix}.csv'),
                         'vwap_target/zhongtai_vwap_target_{}{}.csv'.format(work_date,suffix))

    # uploader.upload_file(os.path.join(local_account_dir, f'zhongtai_vwap_target_{work_date}{suffix}.csv'))


def ftpcopy_t0(work_date, suffix=''):
    local_account_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'accounts', '超量子中泰')
    dav_order_dir = os.path.join(f'/home/<USER>/dav', 'accounts', '超量子中泰', 'order')
    
    # # t0
    shutil.copy(os.path.join(local_account_dir, f'zhongtai_t0_target_{work_date}{suffix}.csv'),
                os.path.join(dav_order_dir, f'zhongtai_t0_target_{work_date}{suffix}.csv')
                )
    
    sftp_clent_wintrader.put(local_path=os.path.join(local_account_dir, f'zhongtai_t0_target_{work_date}{suffix}.csv'),
                             remote_path='交易文件/超量子中泰/zhongtai_t0_target_{}{}.csv'.format(work_date,suffix)
                             )    
    ftp_clent_chaolz.put(os.path.join(local_account_dir, f'zhongtai_t0_target_{work_date}{suffix}.csv'),
                         't0_target/zhongtai_t0_target_{}{}.csv'.format(work_date,suffix))

    # uploader.upload_file(os.path.join(local_account_dir, f'zhongtai_t0_target_{work_date}{suffix}.csv'))

def upload_order_to_zt_tuoguan(work_date, type, suffix=''):
    local_account_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'accounts', '超量子中泰')
    order_file = f'zhongtai_{type}_target_{work_date}{suffix}.csv'
    target_dir = os.path.join('daily_before/target', work_date)
    try:
        sftp_clent_zt_tuoguan.stat(target_dir)
    except FileNotFoundError:
        sftp_clent_zt_tuoguan.mkdir(target_dir)
        
    target_path = os.path.join(target_dir, f'zhongtai_{type}_target_{work_date}.csv')  # 托管服务器文件没有后缀
    sftp_clent_zt_tuoguan.put(os.path.join(local_account_dir, order_file), target_path)


if __name__ == "__main__":
    if len(sys.argv) > 1:
        work_date = str(sys.argv[1])
    else:
        work_date = datetime.datetime.now().strftime('%Y%m%d')
    
    # uploader = ZTHaoMaiUploader()
    # ===========================
    account_name = '1000指增_中泰'
    # hold_type    = 'latest'
    hold_type    = 'from_target'
    # hold_type    = 'as_target'

    target_suffix  = ''
    order_suffix   = ''
    # order_suffix   = '_2'
    
    print(f'\n持仓_type: {hold_type} \n目标_suffix: {target_suffix}  \norder_suffix: {order_suffix}')
    print(f'账户: {account_name}\n交易日: {work_date}\n')
    # print(f'\n交易日: {work_date}\n')
    
    # calc_vwap_target_info(work_date, suffix)
    vwap_target(work_date, hold_type, target_suffix, order_suffix)
    flag_vwap = input('\n   是否上传 vwap_target？(y/n)\n       ')
    if flag_vwap.lower() == 'y':
        ftpcopy_vwap(work_date, order_suffix)
        upload_order_to_zt_tuoguan(work_date, 'vwap', order_suffix)
        print(f'\n上传 vwap_target 完成')

    
    t0_target(work_date, hold_type, target_suffix, order_suffix)
    flag_t0 = input('\n   是否上传 t0_target？(y/n)\n       ')
    if flag_t0.lower() == 'y':
        ftpcopy_t0(work_date, order_suffix)
        upload_order_to_zt_tuoguan(work_date, 't0', order_suffix)
        print(f'\n上传 t0_target 完成')