import pandas as pd
import datetime
import os, sys
from pathlib import Path

notebook_dir = Path.cwd() 

print(str(notebook_dir.parent))
sys.path.insert(0, str(notebook_dir.parent))
from utils import mysql
sys.path.remove(str(notebook_dir.parent))


print(str(notebook_dir.parent.parent))
sys.path.insert(0, str(notebook_dir.parent.parent))
from misc.ssh_conn import ftp_clent_zx_zhongtai
from misc.Readstockfile import read_remote_file, write_file
sys.path.remove(str(notebook_dir.parent.parent))

account_firm_map={'28131077_3104_3104':'kuanfu','9225553_1102_1102':'wuzhi'}
def int2str(x):
    return str(int(x))

def operation_map(x):
    if x==1:
        return 0
    if x==2:
        return 1
    return None

# date="********"
date=datetime.datetime.now().strftime('%Y%m%d')
print(date)

# df=pd.read_csv(r"/home/<USER>/py/stk_py/data/raw_data/data/ld_guojun/tb_tdsest_strategyorder_{}.csv".format(date))
df=pd.read_csv(r"/home/<USER>/dav/ld_guojun/tb_tdsest_strategyorder_{}.csv".format(date))
df=df[df['strategy_exec_broker']=='ldsmart01']

new_df=pd.DataFrame()
new_df['date']=df['create_date'].astype(int)
new_df['id']='guojun_ld_'+df['create_date'].apply(int2str)+df['strategy_id'].apply(int2str)
new_df['operation']=df['strategy_dir'].apply(operation_map)
new_df['create_time']=df['create_time'].astype(int)
new_df['start_time']=df['begin_time'].astype(int)
new_df['end_time']=df['end_time'].astype(int)
new_df['symbol']=df['stock_code'].apply(lambda x:str(int(x)).zfill(6))
new_df['quantity']=df['strategy_qty'].astype(int)
new_df['filled_quantity']=df['strike_qty'].astype(int)
new_df['filled_price']=df['strike_amt']/df['strike_qty']
new_df['account_id']=df['fund_account']
new_df['algo_name']='vwap'
new_df['firm']=df['fund_account'].apply(lambda x:account_firm_map[x])
new_df['algo_provider']='zhishu'
new_df['pm']='xuj'
new_df['broker']='guotaijunan'
new_df['params']=""
new_df['sys_type']="kafang_zongxian"
new_df['remark1']=""
new_df['remark2']=""
new_df['remark3']=""
print(new_df)

mysql.upsert_dataframe(mysql.get_connection("110.42.96.52","zs_trading_data","root","jtwmy,dt4gx",engine=True),new_df,"algo_parentorder",{})

# df=pd.read_csv(r"/home/<USER>/py/stk_py/data/raw_data/data/ld_guojun/tb_tdsest_strategyorder_{}.csv".format(date))
df=pd.read_csv(r"/home/<USER>/dav/ld_guojun/tb_tdsest_strategyorder_{}.csv".format(date))
df=df[df['strategy_exec_broker']=='ldsmart02']
print(df)

new_df=pd.DataFrame()
new_df['date']=df['create_date'].astype(int)
new_df['id']='guojun_ld_'+df['create_date'].apply(int2str)+df['strategy_id'].apply(int2str)
new_df['create_time']=df['create_time'].astype(int)
new_df['start_time']=df['begin_time'].astype(int)
new_df['end_time']=df['end_time'].astype(int)
new_df['symbol']=df['stock_code'].apply(lambda x:str(x).zfill(6))
new_df['quantity']=df['strategy_qty'].astype(int)
new_df['account_id']=df['fund_account']
new_df['algo_name']='aaron_t0_v1'
new_df['firm']=df['fund_account'].apply(lambda x:account_firm_map[x])
new_df['algo_provider']='zhishu'
new_df['pm']='aaron'
new_df['broker']='guotaijunan'
new_df['params']=""
new_df['sys_type']="kafang_zongxian"
new_df['remark1']=""
new_df['remark2']=""
new_df['remark3']=""
print(new_df)
mysql.upsert_dataframe(mysql.get_connection("110.42.96.52","zs_trading_data","root","jtwmy,dt4gx",engine=True),new_df,"intraday_parentorder",{})

new_df

# df=pd.read_csv(r"/home/<USER>/py/stk_py/data/raw_data/data/ld_guojun/tb_tdsetd_order_{}.csv".format(date))
df=pd.read_csv(r"/home/<USER>/dav/ld_guojun/tb_tdsetd_order_{}.csv".format(date),encoding='gbk')
print(df.head(3))

def status_map(s):
    if s=='1':
        return 1
    if s=='2':
        return 2
    if s=='3':
        return 5
    if s=='4':
        return 7
    if s=='5':
        return 3
    if s=='6':
        return 6
    if s=='7':
        return 7
    if s=='8':
        return 9
    return 0

new_df=pd.DataFrame()
new_df['id']='guojun_ld_'+df['create_date'].apply(int2str)+df['order_id'].apply(int2str)
new_df['pid']='guojun_ld_'+df['create_date'].apply(int2str)+df['strategy_id'].apply(int2str)
new_df['create_time']=pd.to_datetime(df['create_date'].apply(int2str)+df['create_time'].apply(lambda x:str(int(x)).zfill(6))).astype('int64') // 1e9
new_df['last_upd_time']=pd.to_datetime(df['create_date'].apply(int2str)+df['update_time'].apply(lambda x:str(int(x)).zfill(6))).astype('int64') // 1e9
new_df['symbol']=df['stock_code'].apply(lambda x:str(x).zfill(6))
new_df['account_id']=df['fund_account']
new_df['operation']=df['order_dir'].apply(operation_map)
new_df['price']=df['order_price']
new_df['quantity']=df['order_qty'].astype(int)
new_df['filled_price']=df['strike_amt']/df['strike_qty']
new_df['filled_quantity']=df['strike_qty'].astype(int)
new_df['status']=df['order_status'].astype(str).apply(status_map)
new_df['order_id']=''
new_df['order_type']=0
new_df['err_msg']=''
new_df['remark1']=''
new_df['remark2']=''
print(new_df.head(3))

mysql.upsert_dataframe(mysql.get_connection("110.42.96.52","zs_trading_data","root","jtwmy,dt4gx",engine=True),new_df,"orders",{})

import pandas as pd

# date="********"

# pos=pd.read_csv(r'/home/<USER>/py/stk_py/data/raw_data/data/zhongtai_tuoguan/parentorder_{}.csv'.format(date))
# basket=pd.read_csv(r'/home/<USER>/py/stk_py/data/raw_data/data/zhongtai_tuoguan/basket_{}.csv'.format(date))
pos=pd.read_csv(r'/data/shared-data/public/vsftp_data/zs_zhongtai/data/daily_after/{}/parentorder_{}.csv'.format(date, date))
basket=pd.read_csv(r'/data/shared-data/public/vsftp_data/zs_zhongtai/data/daily_after/{}/basket_{}.csv'.format(date, date))
basket=basket.rename(columns={basket.columns[0]:'basket_id','开始时间':'start_time','结束时间':'end_time'})
basket=basket[['basket_id','start_time','end_time']]
pos=pos.rename(columns={pos.columns[0]:'symbol','母单编号':'basket_id','成交数量':'filled_quantity','成交金额':'filled_amt','剩余数量':'rem_qty','方向':'side'})
pos=pos[['basket_id','symbol','side','filled_quantity','filled_amt','rem_qty']]
pos=pos.merge(basket,how='left',on='basket_id')
pos['filled_amt']=pos['filled_amt'].str.replace(',','').astype(float)
pos['filled_quantity']=pos['filled_quantity'].str.replace(',','').astype(float)
pos['symbol']=pos['symbol'].apply(lambda x:str(x).zfill(6))
pos['side']=pos['side'].apply(lambda x:0 if x=='买' else 1)
pos['quantity']=pos['filled_quantity']+pos['rem_qty']
pos['filled_price']=pos['filled_amt']/pos['filled_quantity']
pos['start_time']=pos['start_time'].str.replace(':',"")+'00'
pos['end_time']=pos['end_time'].str.replace(':',"")+'00'
pos['create_time']=pos['start_time']    
new_df=pd.DataFrame()
new_df['id']=date+pos['basket_id'].apply(int2str)+pos['symbol']
new_df['operation']=pos['side']
new_df['create_time']=pos['create_time'].astype(int)
new_df['start_time']=pos['start_time'].astype(int)
new_df['end_time']=pos['end_time'].astype(int)
new_df['symbol']=pos['symbol']
new_df['quantity']=pos['quantity'].astype(int)
new_df['filled_quantity']=pos['filled_quantity'].astype(int)  
new_df['filled_price']=pos['filled_price'].astype(float)
new_df['account_id']='clz_zhongtai_109156033251'
new_df['algo_name']='vwap'
new_df['firm']='chaoliangzi'
new_df['algo_provider']='haoxing'
new_df['pm']=''
new_df['broker']='zhongtai'
new_df['params']=""
new_df['sys_type']="xtp"
new_df['remark1']=""
new_df['remark2']=""
new_df['remark3']=""
new_df['date']=date
print(new_df[['start_time', 'end_time']].head())
print(new_df.shape)

mysql.upsert_dataframe(mysql.get_connection("110.42.96.52","zs_trading_data","root","jtwmy,dt4gx",engine=True),new_df,"algo_parentorder",{})

def status_map(s):
    if s==' 全部成交':
        return 6
    if s==' 已撤单':
        return 8
    if s==' 部分撤单':
        return 8
    if s==' 已拒绝':
        return 9
    return 0

# df=pd.read_csv(r'/home/<USER>/py/stk_py/data/raw_data/data/zhongtai_tuoguan/order_{}.csv'.format(date))
df=pd.read_csv(r'/data/shared-data/public/vsftp_data/zs_zhongtai/data/daily_after/{}/order_{}.csv'.format(date,date))
df=df.rename(columns={df.columns[0]:'basket_id','证券代码':'symbol','方向':'side','报单状态':'status','委托价格':'price','委托数量':'quantity','成交数量':'filled_quantity','成交金额':'filled_amt','剩余数量':'rem_qty','委托时间':'time'})
df=df[['basket_id','symbol','side','status','price','quantity','filled_quantity','filled_amt','rem_qty','time','rowId']]
df['quantity']=df['quantity'].str.replace(',','').astype(float)
df['filled_quantity']=df['filled_quantity'].str.replace(',','').astype(float)
df['filled_amt']=df['filled_amt'].str.replace(',','').astype(float)
df['side']=df['side'].apply(lambda x:0 if x=='买' else 1)
df['symbol']=df['symbol'].apply(lambda x:str(x).zfill(6))
new_df=pd.DataFrame()
new_df['id']=df['rowId']
new_df['pid']=date+df['basket_id'].apply(int2str)+df['symbol']
new_df['create_time']=pd.to_datetime(date+" "+df['time']).astype('int64') // 1e9
new_df['last_upd_time']=new_df['create_time']
new_df['symbol']=df['symbol']
new_df['account_id']='clz_zhongtai_109156033251'
new_df['operation']=df['side']
new_df['price']=df['price']
new_df['quantity']=df['quantity'].astype(int)
new_df['filled_price']=(df['filled_amt']/df['filled_quantity']).fillna(0)
new_df['filled_quantity']=df['filled_quantity'].astype(int)
new_df['status']=df['status'].astype(str).apply(status_map)
new_df['order_id']=''
new_df['order_type']=0
new_df['err_msg']=''
new_df['remark1']=''
new_df['remark2']=''

mysql.upsert_dataframe(mysql.get_connection("110.42.96.52","zs_trading_data","root","jtwmy,dt4gx",engine=True),new_df,"orders",{})

# pos=pd.read_csv(r'/home/<USER>/py/stk_py/data/raw_data/data/zhongtai_tuoguan/t0_parentorder_{}.csv'.format(date))
# basket=pd.read_csv(r'/home/<USER>/py/stk_py/data/raw_data/data/zhongtai_tuoguan/t0_basket_{}.csv'.format(date))
pos=pd.read_csv(r'/data/shared-data/public/vsftp_data/zs_zhongtai/data/daily_after/{}/t0_parentorder_{}.csv'.format(date,date))
basket=pd.read_csv(r'/data/shared-data/public/vsftp_data/zs_zhongtai/data/daily_after/{}/t0_basket_{}.csv'.format(date,date))

basket=basket.rename(columns={basket.columns[0]:'basket_id','开始时间':'start_time','结束时间':'end_time'})
basket=basket[['basket_id','start_time','end_time']]

pos=pos.rename(columns={pos.columns[0]:'symbol','母单编号':'basket_id','标的数量':'quantity'})
pos=pos[['basket_id','symbol','quantity']]
pos=pos.merge(basket,how='left',on='basket_id')

pos['quantity']=pos['quantity'].str.replace(',','').astype(float)
pos['symbol']=pos['symbol'].apply(lambda x:str(x).zfill(6))
pos['start_time']=pos['start_time'].str.replace(':',"")+'00'
pos['end_time']=pos['end_time'].str.replace(':',"")+'00'
pos['create_time']=pos['start_time']    

new_df=pd.DataFrame()
new_df['id']=date+pos['basket_id'].apply(int2str)+pos['symbol']
new_df['create_time']=pos['create_time'].astype(int)
new_df['start_time']=pos['start_time'].astype(int)
new_df['end_time']=pos['end_time'].astype(int)
new_df['symbol']=pos['symbol']
new_df['quantity']=pos['quantity'].astype(int)
new_df['account_id']='clz_zhongtai_109156033251'
new_df['algo_name']='yueran_t0'
new_df['firm']='chaoliangzi'
new_df['algo_provider']='yueran'
new_df['pm']=''
new_df['broker']='zhongtai'
new_df['params']=""
new_df['sys_type']="XTP"
new_df['remark1']=""
new_df['remark2']=""
new_df['remark3']=""
new_df['date']=date

mysql.upsert_dataframe(mysql.get_connection("110.42.96.52","zs_trading_data","root","jtwmy,dt4gx",engine=True),new_df,"intraday_parentorder",{})

def status_map(s):
    if s==' 全部成交':
        return 6
    if s==' 已撤单':
        return 8
    if s==' 部分撤单':
        return 8
    if s==' 已拒绝':
        return 9
    return 0

# df=pd.read_csv(r'/home/<USER>/py/stk_py/data/raw_data/data/zhongtai_tuoguan/t0_order_{}.csv'.format(date))
df=pd.read_csv(r'/data/shared-data/public/vsftp_data/zs_zhongtai/data/daily_after/{}/t0_order_{}.csv'.format(date,date))

df=df.rename(columns={df.columns[0]:'basket_id','证券代码':'symbol','方向':'side','报单状态':'status','委托价格':'price','委托数量':'quantity','成交数量':'filled_quantity','成交金额':'filled_amt','剩余数量':'rem_qty','委托时间':'time'})
df=df[['basket_id','symbol','side','status','price','quantity','filled_quantity','filled_amt','rem_qty','time','rowId']]
df['quantity']=df['quantity'].str.replace(',','').astype(float)
df['filled_quantity']=df['filled_quantity'].str.replace(',','').astype(float)
df['filled_amt']=df['filled_amt'].str.replace(',','').astype(float)
df['side']=df['side'].apply(lambda x:0 if x=='买' else 1)
df['symbol']=df['symbol'].apply(lambda x:str(x).zfill(6))

new_df=pd.DataFrame()
new_df['id']=df['rowId']
new_df['pid']=date+df['basket_id'].apply(int2str)+df['symbol']
new_df['create_time']=pd.to_datetime(date+" "+df['time']).astype('int64') // 1e9
new_df['last_upd_time']=new_df['create_time']
new_df['symbol']=df['symbol']
new_df['account_id']='clz_zhongtai_109156033251'
new_df['operation']=df['side']
new_df['price']=df['price']
new_df['quantity']=df['quantity'].astype(int)
new_df['filled_price']=(df['filled_amt']/df['filled_quantity']).fillna(0)
new_df['filled_quantity']=df['filled_quantity'].astype(int)
new_df['status']=df['status'].astype(str).apply(status_map)
new_df['order_id']=''
new_df['order_type']=0
new_df['err_msg']=''
new_df['remark1']=''
new_df['remark2']=''

mysql.upsert_dataframe(mysql.get_connection("110.42.96.52","zs_trading_data","root","jtwmy,dt4gx",engine=True),new_df,"orders",{})


#===================================================================================

# df=pd.read_csv(r"/home/<USER>/py/stk_py/data/raw_data/data/ld_guojun/tb_tdsest_strategyorder.csv")
df=pd.read_csv(r"/home/<USER>/py/stk_py/data/raw_data/data/ld_guojun/tb_tdsest_strategyorder.csv")

df[df['fund_account']==''].unique()

df[df['strategy_exec_broker']=='ldsmart02']['fund_account'].unique()

from data import data_reader

pxs=data_reader.get_close_pxs_by_date_from_mysql("********")

pxs

df.columns

df.iloc[0]

df=df[df['target_strategy_type']=='VWAP']

df['px']=df['stock_code'].apply(lambda x:str(x).zfill(6)).apply(lambda x:pxs[x])

df['amt']=df['strategy_qty']*df['px']

df['amt'].sum()

df.groupby('fund_account')['amt'].sum()

# import leveldb
import plyvel
import datetime
import json
import pandas as pd
from utils import mysql
import os
# def read_algo_data(p):
#     # ldb=leveldb.LevelDB(p)
#     ldb = plyvel.DB(p)
#     l=[]
#     datas=list(ldb.RangeIter())
#     # true='true'
#     pos=[]
#     for k,v in datas:
#         d=json.loads(v)
#         pos.append(d) 
#     po_df=pd.DataFrame(pos)
#     ldb=None
#     return po_df 

def read_algo_data(p):
    # 1. 打开数据库
    db = plyvel.DB(p, create_if_missing=False)

    # 2. 获取迭代器对象
    iterator = db.iterator()  # 创建迭代器

    # 3. 遍历所有键值对（方法1：手动遍历迭代器）
    pos = []
    for k, v in iterator:  # 直接遍历迭代器（默认从第一个键开始）
        d = json.loads(v.decode('utf-8'))  # 解码 bytes 并解析 JSON
        pos.append(d)

    # 4. 关闭数据库
    db.close()

    # 5. 返回 DataFrame
    return pd.DataFrame(pos)

def operation_map(x):
    if x==100:
        return 0
    if x==110:
        return 1
    return None

def status_map(s):
    if s==0:
        return 1
    if s==10:
        return 2
    if s==20:
        return 2
    if s==30:
        return 3
    if s==40:
        return 5
    if s==100:
        return 6
    if s==110:
        return 7
    if s==120:
        return 9
    return 0

# date="20250530"
date=datetime.datetime.now().strftime('%Y%m%d')
pt=r'/data/shared-data/public/algo_trading_data/xuntou/parentorder_{}.ldb'.format(date)
df=read_algo_data(pt)
print(df)

df['create_time']=(pd.to_datetime(df['create_time'],unit='ms')+pd.Timedelta(hours=8)).apply(lambda datetime_val:datetime_val.hour * 10000 + datetime_val.minute * 100 + datetime_val.second)
df['start_time']=(pd.to_datetime(df['start_time'],unit='ms')+pd.Timedelta(hours=8)).apply(lambda datetime_val:datetime_val.hour * 10000 + datetime_val.minute * 100 + datetime_val.second)
df['end_time']=(pd.to_datetime(df['end_time'],unit='ms')+pd.Timedelta(hours=8)).apply(lambda datetime_val:datetime_val.hour * 10000 + datetime_val.minute * 100 + datetime_val.second)
df['operation']=df['operation'].apply(operation_map)
df['status']=df['status'].apply(status_map)
df['firm']='unknown'
df['algo_provider']='zhishu'
df['pm']='xj'
df['date']=date
df['broker']='unknown'
df['sys_type']='xuntou'
df['remark1']=''
df['remark2']=''
df['remark3']=''
df=df[['id','symbol','quantity','operation','create_time','start_time','end_time','filled_quantity','filled_price','algo_name','account_id','firm','algo_provider','pm','date','broker','params','sys_type','remark1','remark2','remark3']]

mysql.upsert_dataframe(mysql.get_connection("110.42.96.52","zs_trading_data","root","jtwmy,dt4gx",engine=True),df,"algo_parentorder",{'date':date,'sys_type':'xuntou'})

pt=r'/data/shared-data/public/algo_trading_data/xuntou/order_{}.ldb'.format(date)
df=read_algo_data(pt)
print(df)
print(df.columns)

df['create_time']=(pd.to_datetime(df['create_time'],unit='ms')+pd.Timedelta(hours=8)).apply(lambda datetime_val:datetime_val.hour * 10000 + datetime_val.minute * 100 + datetime_val.second)
df['last_upd_time']=(pd.to_datetime(df['last_upd_time'],unit='ms')+pd.Timedelta(hours=8)).apply(lambda datetime_val:datetime_val.hour * 10000 + datetime_val.minute * 100 + datetime_val.second)
df['operation']=df['operation'].apply(operation_map)
df['status']=df['status'].apply(status_map)
df['firm']='unknown'
df['algo_provider']='zhishu'
df['pm']='xj'
df['date']=date
df['broker']='unknown'
df['sys_type']='xuntou'
df['remark1']=''
df['remark2']=''
# df['err_msg']=df['error_msg']
df['err_msg'] = ''

df=df[['id','pid','symbol','create_time','last_upd_time','account_id','operation','price','quantity','filled_price','filled_quantity','status','order_id','order_type','err_msg','remark1','remark2']]
print(df)

mysql.upsert_dataframe(mysql.get_connection("110.42.96.52","zs_trading_data","root","jtwmy,dt4gx",engine=True),df,"orders",{})

